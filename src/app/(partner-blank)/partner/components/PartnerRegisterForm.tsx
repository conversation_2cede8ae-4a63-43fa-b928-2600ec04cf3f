import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { PartnerSignupFormSchema } from '@/schemas/partner';
import { PartnerSignupFormData } from '@/schemas/partner';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import EmailInput from './EmailInput';
import PasswordInput from './PasswordInput';
import PhoneVerificationInput from './PhoneVerificationInput';

interface PartnerRegisterFormProps {
  className?: string;
  onSubmit: (data: PartnerSignupFormData) => Promise<void>;
  loading?: boolean;
}

export const PartnerRegisterForm: React.FC<PartnerRegisterFormProps> = ({
  className,
  onSubmit,
  loading = false,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const [registerError, setRegisterError] = useState<string | null>(null);
  const [verificationSent, setVerificationSent] = useState(false);
  const [sendingVerification, setSendingVerification] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');

  const form = useForm<PartnerSignupFormData>({
    resolver: zodResolver(PartnerSignupFormSchema),
    defaultValues: {
      email: '',
      password: '',
      passwordConfirm: '',
      contactInfo: {
        name: '',
        phone: '',
      },
    },
  });

  const {
    watch,
    setValue,
    formState: { errors },
  } = form;

  const email = watch('email');
  const password = watch('password');
  const passwordConfirm = watch('passwordConfirm');
  const contactName = watch('contactInfo.name');
  const contactPhone = watch('contactInfo.phone');

  const handleSubmit = async (data: PartnerSignupFormData) => {
    setRegisterError(null);

    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Register error:', error);
      setRegisterError(
        '회원가입 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.'
      );
    }
  };

  const handleSendVerification = async (phoneNumber: string) => {
    setSendingVerification(true);
    try {
      // TODO: 실제 인증번호 전송 API 호출
      await new Promise(resolve => setTimeout(resolve, 1000)); // 임시 딜레이
      setVerificationSent(true);
    } catch (error) {
      console.error('인증번호 전송 실패:', error);
    } finally {
      setSendingVerification(false);
    }
  };

  return (
    <div className={cn('flex flex-1 flex-col', className)}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className='flex flex-1 flex-col justify-between'
      >
        {/* 입력 필드들 */}
        <div className='flex flex-col gap-4'>
          {/* 이메일 입력 */}
          <EmailInput
            value={email}
            onChange={value => setValue('email', value)}
            error={errors.email?.message}
            disabled={loading}
            required
          />

          {/* 비밀번호 입력 */}
          <PasswordInput
            value={password}
            onChange={value => setValue('password', value)}
            error={errors.password?.message}
            disabled={loading}
            required
            showPassword={showPassword}
            onToggleShow={() => setShowPassword(!showPassword)}
          />

          {/* 비밀번호 확인 입력 */}
          <PasswordInput
            value={passwordConfirm}
            onChange={value => setValue('passwordConfirm', value)}
            error={errors.passwordConfirm?.message}
            disabled={loading}
            required
            showPassword={showPasswordConfirm}
            onToggleShow={() => setShowPasswordConfirm(!showPasswordConfirm)}
            label='비밀번호 확인'
            id='passwordConfirm'
            placeholder='비밀번호를 다시 입력하세요'
            confirmValue={password}
            isConfirmField={true}
          />

          {/* 담당자명 입력 */}
          {/* <div className='space-y-2'>
            <Label
              htmlFor='contactName'
              className='text-sm font-medium text-gray-700'
            >
              담당자명
            </Label>
            <Input
              id='contactName'
              type='text'
              value={contactName}
              onChange={e => setValue('contactInfo.name', e.target.value)}
              placeholder='담당자 성함을 입력하세요'
              disabled={loading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.contactInfo?.name && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.contactInfo?.name && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.contactInfo.name.message}
              </p>
            )}
          </div> */}

          {/* 휴대폰 번호 + 인증번호 전송 */}
          <PhoneVerificationInput
            value={contactPhone}
            onChange={value => setValue('contactInfo.phone', value)}
            error={errors.contactInfo?.phone?.message}
            disabled={loading}
            required
            onSendVerification={handleSendVerification}
            verificationSent={verificationSent}
            sendingVerification={sendingVerification}
          />

          {/* 인증번호 입력 (인증번호가 전송된 경우에만 표시) */}
          {verificationSent && (
            <div className='space-y-2'>
              <Label
                htmlFor='verificationCode'
                className='text-sm font-medium text-gray-700'
              >
                인증번호
              </Label>
              <Input
                id='verificationCode'
                type='text'
                value={verificationCode}
                onChange={e => setVerificationCode(e.target.value)}
                placeholder='인증번호 6자리를 입력하세요'
                disabled={loading}
                maxLength={6}
                className={cn(
                  'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500'
                )}
              />
              <p className='text-sm text-gray-500'>
                휴대폰으로 전송된 6자리 인증번호를 입력해주세요.
              </p>
            </div>
          )}

          {/* 에러 메시지 */}
          {registerError && (
            <div className='rounded-md bg-red-50 p-4'>
              <div className='flex'>
                <div className='flex-shrink-0'>
                  <svg
                    className='h-5 w-5 text-red-400'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
                    />
                  </svg>
                </div>
                <div className='ml-3'>
                  <p className='text-sm text-red-800'>{registerError}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 회원가입 버튼 */}
        <div className='flex flex-col gap-2'>
          <Button
            type='submit'
            disabled={loading}
            className='h-10 w-full rounded-lg bg-gradient-to-r from-[#5000D0] to-[#8645EF] font-semibold text-white transition-colors hover:from-[#5000D0]/80 hover:to-[#8645EF]/80'
          >
            {loading ? (
              <div className='flex items-center justify-center'>
                <svg
                  className='mr-3 -ml-1 h-5 w-5 animate-spin text-white'
                  fill='none'
                  viewBox='0 0 24 24'
                >
                  <circle
                    className='opacity-25'
                    cx='12'
                    cy='12'
                    r='10'
                    stroke='currentColor'
                    strokeWidth='4'
                  />
                  <path
                    className='opacity-75'
                    fill='currentColor'
                    d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                  />
                </svg>
                회원가입 중...
              </div>
            ) : (
              '파트너 가입하기'
            )}
          </Button>

          {/* 로그인 링크 */}
          <div className='text-center'>
            <span className='text-sm text-gray-600'>
              이미 계정이 있으신가요?{' '}
              <Link
                href='/partner/login'
                className='text-primary hover:text-primary/50 font-medium transition-colors'
              >
                로그인하기
              </Link>
            </span>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PartnerRegisterForm;
