'use client';

import { forwardRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { PhoneInput } from '@/components/partner/common/PhoneInput';
import { cn } from '@/lib/utils';

interface PhoneVerificationInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  onSendVerification?: (phoneNumber: string) => Promise<void>;
  verificationSent?: boolean;
  sendingVerification?: boolean;
}

export const PhoneVerificationInput = forwardRef<
  HTMLInputElement,
  PhoneVerificationInputProps
>(
  (
    {
      value,
      onChange,
      error,
      disabled = false,
      required = false,
      className,
      onSendVerification,
      verificationSent = false,
      sendingVerification = false,
      ...props
    },
    ref
  ) => {
    const [localSent, setLocalSent] = useState(false);
    const isSent = verificationSent || localSent;

    const handleSendVerification = async () => {
      if (!value || !onSendVerification) return;

      try {
        await onSendVerification(value);
        setLocalSent(true);
      } catch (error) {
        console.error('인증번호 전송 실패:', error);
      }
    };

    const isValidPhone = value && /^010-\d{4}-\d{4}$/.test(value);

    return (
      <div className={cn('space-y-2', className)}>
        <div className='flex items-center gap-2'>
          <div className='flex-1'>
            <PhoneInput
              ref={ref}
              value={value}
              onChange={onChange}
              error={error}
              disabled={disabled}
              required={required}
              label='휴대폰 번호'
              placeholder='010-0000-0000'
              {...props}
            />
          </div>
          <div className='flex items-end'>
            <Button
              type='button'
              variant='outline'
              size='sm'
              onClick={handleSendVerification}
              disabled={
                !isValidPhone || disabled || sendingVerification || isSent
              }
              className={cn(
                'h-10 px-3 text-sm whitespace-nowrap',
                isSent && 'border-green-200 bg-green-50 text-green-700'
              )}
            >
              {sendingVerification ? (
                <div className='flex items-center gap-1'>
                  <svg
                    className='h-3 w-3 animate-spin'
                    fill='none'
                    viewBox='0 0 24 24'
                  >
                    <circle
                      className='opacity-25'
                      cx='12'
                      cy='12'
                      r='10'
                      stroke='currentColor'
                      strokeWidth='4'
                    />
                    <path
                      className='opacity-75'
                      fill='currentColor'
                      d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                    />
                  </svg>
                  전송중
                </div>
              ) : isSent ? (
                '전송완료'
              ) : (
                '인증번호 전송'
              )}
            </Button>
          </div>
        </div>

        {isSent && !error && (
          <p className='flex items-center gap-1 text-sm text-green-600'>
            <svg
              className='h-4 w-4 flex-shrink-0'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M5 13l4 4L19 7'
              />
            </svg>
            인증번호가 전송되었습니다.
          </p>
        )}
      </div>
    );
  }
);

PhoneVerificationInput.displayName = 'PhoneVerificationInput';

export default PhoneVerificationInput;
