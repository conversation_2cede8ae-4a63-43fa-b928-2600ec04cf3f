'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type { PartnerSignupFormData } from '@/schemas/partner';
import type { PartnerRegisterResponse } from '@/types/partner';
import PartnerRegisterForm from '../components/PartnerRegisterForm';

export default function PartnerRegisterPage() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (data: PartnerSignupFormData) => {
    setLoading(true);

    try {
      const response = await fetch('/api/partner/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result: PartnerRegisterResponse = await response.json();

      if (!response.ok) {
        // 409 Conflict - 이메일 중복 또는 파트너 중복
        if (response.status === 409) {
          toast.error('이미 등록된 이메일 주소입니다', {
            description: '다른 이메일로 다시 시도해주세요.',
          });
        }
        // 400 Bad Request - 입력 오류
        else if (response.status === 400) {
          toast.error('입력 정보를 확인해주세요', {
            description: result.message || '올바른 정보를 입력해주세요.',
          });
        }
        // 기타 오류
        else {
          toast.error('회원가입 중 오류가 발생했습니다', {
            description: result.message || '잠시 후 다시 시도해주세요.',
          });
        }

        return;
      }

      // 성공 시 성공 메시지와 함께 로그인 페이지로 이동
      toast.success('회원 가입이 완료되었습니다.', {
        description: '로그인 페이지로 이동합니다.',
      });

      router.push('/partner/login');
    } catch (err) {
      console.error('회원가입 오류:', err);

      toast.error('네트워크 오류가 발생했습니다', {
        description: '인터넷 연결을 확인하고 다시 시도해주세요.',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex w-full max-w-md flex-1 flex-col gap-10 py-12'>
      {/* 타이틀 섹션 */}
      <div className='text-left'>
        <h1 className='bg-gradient-to-r from-[#5530FF] to-[#9D43FF] bg-clip-text text-3xl font-bold text-transparent'>
          SHALLWEE
        </h1>
        <p className='text-2xl font-semibold'>파트너 가입</p>
      </div>
      <PartnerRegisterForm onSubmit={handleSubmit} loading={loading} />
    </div>
  );
}
